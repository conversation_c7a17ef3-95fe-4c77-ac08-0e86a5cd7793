{"openapi": "3.0.4", "info": {"title": "Web.Api", "version": "1.0"}, "paths": {"/jobs": {"post": {"tags": ["jobs"], "operationId": "<PERSON><PERSON><PERSON><PERSON>", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Web.Api.Endpoints.Jobs.CreateJobRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "string", "format": "uuid"}}}}, "400": {"description": "Bad Request", "content": {"application/problem+json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Http.HttpValidationProblemDetails"}}}}}}}, "/jobs/{id}": {"put": {"tags": ["jobs"], "operationId": "Update<PERSON><PERSON>", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Web.Api.Endpoints.Jobs.UpdateJobRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "string", "format": "uuid"}}}}, "400": {"description": "Bad Request", "content": {"application/problem+json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Http.HttpValidationProblemDetails"}}}}}}}, "/resumes": {"post": {"tags": ["resumes"], "operationId": "CreateResume", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Web.Api.Endpoints.Resumes.CreateResume-Request"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"application/problem+json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Http.HttpValidationProblemDetails"}}}}}}}, "/resumes/{id}": {"get": {"tags": ["resumes"], "operationId": "GetResume", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "boolean", "default": true}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Application.Resumes.GetById.ResumeResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/problem+json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Http.HttpValidationProblemDetails"}}}}}}}, "/resumes/upload": {"post": {"tags": ["resumes"], "operationId": "CreateResumeFromFile", "parameters": [{"name": "userId", "in": "query", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "parentId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["file"], "type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "string", "format": "uuid"}}}}, "400": {"description": "Bad Request", "content": {"application/problem+json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Http.HttpValidationProblemDetails"}}}}}}}, "/todos/{id}/complete": {"put": {"tags": ["todos"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/todos": {"post": {"tags": ["todos"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Web.Api.Endpoints.Todos.Create-Request"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}, "get": {"tags": ["todos"], "parameters": [{"name": "userId", "in": "query", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/todos/{id}": {"delete": {"tags": ["todos"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}, "get": {"tags": ["todos"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/users/{userId}": {"get": {"tags": ["Users"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/users/login": {"post": {"tags": ["Users"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Web.Api.Endpoints.Users.Login-Request"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/users/register": {"post": {"tags": ["Users"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Web.Api.Endpoints.Users.Register-Request"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}}, "components": {"schemas": {"Application.Resumes.GetById.ResumeResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "userId": {"type": "string", "format": "uuid"}, "parentId": {"type": "string", "format": "uuid", "nullable": true}, "resumeContent": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "lastModifiedAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "Microsoft.AspNetCore.Http.HttpValidationProblemDetails": {"type": "object", "properties": {"type": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32", "nullable": true}, "detail": {"type": "string", "nullable": true}, "instance": {"type": "string", "nullable": true}, "errors": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}, "nullable": true}}, "additionalProperties": {}}, "Web.Api.Endpoints.Jobs.CreateJobRequest": {"type": "object", "properties": {"userId": {"type": "string", "format": "uuid"}, "jobTitle": {"type": "string", "nullable": true}, "jobDescription": {"type": "string", "nullable": true}, "jobPostingUrl": {"type": "string", "nullable": true}, "companyUrl": {"type": "string", "nullable": true}, "appliedAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "Web.Api.Endpoints.Jobs.UpdateJobRequest": {"type": "object", "properties": {"jobTitle": {"type": "string", "nullable": true}, "jobDescription": {"type": "string", "nullable": true}, "jobPostingUrl": {"type": "string", "nullable": true}, "companyUrl": {"type": "string", "nullable": true}, "appliedAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "Web.Api.Endpoints.Resumes.CreateResume-Request": {"type": "object", "properties": {"userId": {"type": "string", "format": "uuid"}, "parentId": {"type": "string", "format": "uuid", "nullable": true}, "resumeContent": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Web.Api.Endpoints.Todos.Create-Request": {"type": "object", "properties": {"userId": {"type": "string", "format": "uuid"}, "description": {"type": "string", "nullable": true}, "dueDate": {"type": "string", "format": "date-time", "nullable": true}, "labels": {"type": "array", "items": {"type": "string"}, "nullable": true}, "priority": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "Web.Api.Endpoints.Users.Login-Request": {"type": "object", "properties": {"email": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Web.Api.Endpoints.Users.Register-Request": {"type": "object", "properties": {"email": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "http", "description": "Enter your JWT token in this field", "scheme": "Bearer", "bearerFormat": "JWT"}}}, "security": [{"Bearer": []}]}